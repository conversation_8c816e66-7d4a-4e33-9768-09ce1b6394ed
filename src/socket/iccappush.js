var fs=require("fs");
var path=require('path');


process.env.NODE_TLS_REJECT_UNAUTHORIZED = "0";
const WebSocket = require('ws');

test();

function test(){
	var gg_socket_err_msg;
	var gg_websocket;

	console.log("Created gg_websocket");
	gg_websocket = new WebSocket("wss://" + "updateadhaar.com" + ':31040');
	var gg_websockettimeoutid = null;

	gg_websocket.onopen = function (evt)
	{
		console.log("in onopen");


		var data =
		{
		   op:'saveupdown',
		   website:'indiacustomercare.com',
		   nid:23, //any unique id
		   phone:'**********',
		   data:
		   {
			up:1,   //can be 0 or 1
			down:0  //can be 0 or -1
		   },

		   l: 'valid-license', //licensekey
		   src:'iccapp',
		   created:**********, //time in ms
		   checksum: 40449494 //formula based

		};
		//remove the license


		ff_sendRequest(data, gg_websocket, null);


	} //on open event
	gg_websocket.onclose = function (evt)
	{ /* do stuff */
		gg_websocket = null;
	}; //on close event
	gg_websocket.onmessage = function (evt)
	{
		console.log('Server response:%o ', evt.data);
		try
		{
			var obj = (evt.data);
			console.log(obj);


		}
		catch (ex5)
		{
			console.log("Exception caught:%o", ex5);
		}
		//gg_websocket.close()
	}; //on message event
	gg_websocket.onerror = function (evt)
	{
		gg_socket_err_msg = "Some problem with connecting server";
		console.log("In onerror2 : %O", evt);

		try
		{
			gg_websocket.close();
			gg_websocket = null;
		}
		catch (e3)
		{
			console.log(e3);
		}
	}; //on error event




	function ff_sendRequest(request, gg_websocket, gg_public_key)
	{
		//var securedjson = JSON.stringify(request.secured);
		//console.log("trying to encrypt:" + securedjson);
		request.source = 128;  //this will identify the client
		request.dataversion = "NotEncrypted-v1.0";
		request.version = 'snd1.0';
		request.new_html_version = '1';

		//request.secured = ff_pid_do_encrypt(securedjson, 'encrypt', gg_public_key);

		var payload = JSON.stringify(request);
		console.log("payload length=" + payload.length);
		console.log(payload);


		// Wait until the state of the socket is not ready and send the message when it is...
		waitForSocketConnection(gg_websocket, function ()
				{
					console.log("message sent!!!");
					gg_websocket.send(payload);
				});
	}

	// Make the function wait until the connection is made...
	function waitForSocketConnection(socket, callback)
	{
		setTimeout(
				function ()
				{
					if (socket.readyState === 1)
		{
			console.log("Connection is made")
			if (callback != null)
		{
			callback();
		}
		return;

		}
					else
		{
			//console.log("wait for connection...")
			waitForSocketConnection(socket, callback);
		}

				}, 50); // wait 5 milisecond for the connection...
	}

}

