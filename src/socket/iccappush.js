// React Native WebSocket Service for Vote Functionality
class WebSocketVoteService {
  constructor() {
    this.websocket = null;
    this.isConnecting = false;
    this.messageQueue = [];
  }

  // Initialize WebSocket connection
  connect() {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      return Promise.resolve();
    }

    if (this.isConnecting) {
      return new Promise(resolve => {
        const checkConnection = () => {
          if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
            resolve();
          } else {
            setTimeout(checkConnection, 100);
          }
        };
        checkConnection();
      });
    }

    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        this.websocket = new WebSocket('wss://updateadhaar.com:31040');

        this.websocket.onopen = () => {
          console.log('WebSocket connected for voting');
          this.isConnecting = false;

          // Process any queued messages
          while (this.messageQueue.length > 0) {
            const queuedData = this.messageQueue.shift();
            this.sendVoteData(queuedData);
          }

          resolve();
        };

        this.websocket.onclose = () => {
          console.log('WebSocket connection closed');
          this.websocket = null;
          this.isConnecting = false;
        };

        this.websocket.onmessage = evt => {
          console.log('Vote response from server:', evt.data);
          try {
            const response = JSON.parse(evt.data);
            console.log('Parsed vote response:', response);
          } catch (ex) {
            console.log('Exception parsing vote response:', ex);
          }
        };

        this.websocket.onerror = evt => {
          console.log('WebSocket error:', evt);
          this.isConnecting = false;
          this.websocket = null;
          reject(evt);
        };
      } catch (error) {
        console.log('Error creating WebSocket:', error);
        this.isConnecting = false;
        reject(error);
      }
    });
  }

  // Send vote data to server
  async sendVote(phoneNumber, isUpvote) {
    const voteData = {
      op: 'saveupdown',
      website: 'indiacustomercare.com',
      nid: Date.now(), // Use timestamp as unique id
      phone: phoneNumber,
      data: {
        up: isUpvote ? 1 : 0,
        down: isUpvote ? 0 : -1,
      },
      l: 'valid-license', // license key
      src: 'iccapp',
      created: Date.now(),
      checksum: Math.floor(Math.random() * 100000000), // Random checksum for now
    };

    try {
      await this.connect();
      this.sendVoteData(voteData);
    } catch (error) {
      console.log('Failed to connect WebSocket, queuing message:', error);
      // Queue the message for later sending
      this.messageQueue.push(voteData);
    }
  }

  // Internal method to send data through WebSocket
  sendVoteData(voteData) {
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      console.log('WebSocket not ready, queuing message');
      this.messageQueue.push(voteData);
      return;
    }

    // Add required fields
    voteData.source = 128; // Client identifier
    voteData.dataversion = 'NotEncrypted-v1.0';
    voteData.version = 'snd1.0';
    voteData.new_html_version = '1';

    const payload = JSON.stringify(voteData);
    console.log('Sending vote payload:', payload);

    this.waitForSocketConnection(this.websocket, () => {
      console.log('Vote message sent!');
      this.websocket.send(payload);
    });
  }

  // Wait for socket connection helper
  waitForSocketConnection(socket, callback) {
    setTimeout(() => {
      if (socket.readyState === 1) {
        console.log('Socket connection ready');
        if (callback != null) {
          callback();
        }
        return;
      } else {
        this.waitForSocketConnection(socket, callback);
      }
    }, 50);
  }

  // Close WebSocket connection
  disconnect() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }
}

// Export singleton instance
export default new WebSocketVoteService();
